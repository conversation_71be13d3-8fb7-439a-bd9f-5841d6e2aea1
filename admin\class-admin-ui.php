<?php
if ( ! defined( 'ABSPATH' ) ) exit;

class WAM_Admin_UI {
    public static function init() {
        add_filter( 'manage_team_member_posts_columns', [ __CLASS__, 'columns' ] );
        add_action( 'manage_team_member_posts_custom_column', [ __CLASS__, 'column_content' ], 10, 2 );
        // Register settings
        add_action( 'admin_init', [ __CLASS__, 'register_settings' ] );
        add_action( 'admin_enqueue_scripts', [ __CLASS__, 'enqueue_settings_scripts' ] );
        add_action('admin_enqueue_scripts', function() {
            wp_enqueue_style('wp-color-picker');
            wp_enqueue_script('wp-color-picker');
            add_action('admin_footer', function() {
                echo '<script>jQuery(document).ready(function($){ $(".wam-color-picker").wpColorPicker(); });</script>';
            });
        });
    }

    public static function register_settings() {
        register_setting( 'wam_settings_group', 'wam_global_settings' );
        // Add a section with a dummy callback to avoid blank page
        add_settings_section( 'wam_settings_main', '', [__CLASS__, 'section_dummy'], 'wam-settings' );
        // Fields
        $fields = [
            ['id' => 'main_color', 'title' => 'Main Color', 'type' => 'color', 'desc' => 'Choose Main Color for plugin'],
            ['id' => 'content_font_family', 'title' => 'Content Font Family', 'type' => 'text', 'desc' => 'Enter Google font-family name. For example, if you choose "Source Sans Pro" Google Font, enter Source Sans Pro', 'placeholder' => 'Roboto'],
            ['id' => 'content_font_size', 'title' => 'Content Font Size', 'type' => 'text', 'desc' => 'Enter size of main font, default:13px, Ex: 14px', 'placeholder' => '18'],
            ['id' => 'content_font_color', 'title' => 'Content Font Color', 'type' => 'color', 'desc' => 'Choose Content Font Color for plugin'],
            ['id' => 'heading_font_family', 'title' => 'Heading Font Family', 'type' => 'text', 'desc' => 'Enter Google font-family name. For example, if you choose "Oswald" Google Font, enter Oswald', 'placeholder' => 'Bebas Neue'],
            ['id' => 'heading_font_size', 'title' => 'Heading Font Size', 'type' => 'text', 'desc' => 'Enter size of heading font, default: 20px, Ex: 22px', 'placeholder' => '26'],
            ['id' => 'heading_font_color', 'title' => 'Heading Font Color', 'type' => 'color', 'desc' => 'Choose Heading Font Color for plugin'],
            ['id' => 'meta_font_family', 'title' => 'Meta Font Family', 'type' => 'text', 'desc' => 'Enter Google font-family name. For example, if you choose "Ubuntu" Google Font, enter Ubuntu', 'placeholder' => ''],
            ['id' => 'meta_font_size', 'title' => 'Meta Font Size', 'type' => 'text', 'desc' => 'Enter size of metadata font, default:13px, Ex: 12px', 'placeholder' => ''],
            ['id' => 'meta_font_color', 'title' => 'Meta Font Color', 'type' => 'color', 'desc' => 'Choose Meta Font Color for plugin'],
            ['id' => 'disable_link', 'title' => 'Disable link & Single member page', 'type' => 'select', 'options' => ['no' => 'No', 'yes' => 'Yes'], 'desc' => 'Select yes to disable link to single member page'],
            ['id' => 'rtl_mode', 'title' => 'RTL mode', 'type' => 'select', 'options' => ['no' => 'No', 'yes' => 'Yes'], 'desc' => 'Enable RTL mode for RTL language'],
            ['id' => 'team_slug', 'title' => 'Team slug', 'type' => 'text', 'desc' => 'Remember to save the permalink settings again in Settings > Permalinks', 'placeholder' => 'partners'],
            ['id' => 'disable_social', 'title' => 'Disable social account', 'type' => 'select', 'options' => ['no' => 'No', 'yes' => 'Yes'], 'desc' => 'Select yes to disable social account'],
            // Extra helpful fields for plugin design
            ['id' => 'card_bg_color', 'title' => 'Card Background Color', 'type' => 'color', 'desc' => 'Set the background color for team member cards'],
            ['id' => 'card_border_radius', 'title' => 'Card Border Radius', 'type' => 'text', 'desc' => 'Set the border radius for cards, e.g. 12px', 'placeholder' => '12px'],
        ];
        foreach ($fields as $field) {
            $callback = [__CLASS__, 'render_' . $field['type'] . '_field'];
            if (is_callable($callback)) {
                add_settings_field('wam_'.$field['id'], $field['title'], $callback, 'wam-settings', 'wam_settings_main', $field);
            }
        }
    }

    public static function section_dummy() { /* Output nothing,  blank page */ }

    public static function render_color_field($args) {
        $options = get_option('wam_global_settings');
        $value = isset($options[$args['id']]) ? $options[$args['id']] : '';
        $label = $args['title'];
        $input_id = 'wam_color_' . $args['id'];
        echo '<div style="background:#fff;border:1px solid #e0e0e0;border-radius:8px;padding:18px 18px 8px 18px;margin-bottom:18px;box-shadow:0 1px 2px rgba(0,0,0,0.03);max-width:480px;">';
        echo '<label for="'.$input_id.'" style="font-weight:600;color:#1d2327;display:block;margin-bottom:8px;">'.$label.'</label>';
        echo '<input type="text" id="'.$input_id.'" name="wam_global_settings['.$args['id'].']" value="'.esc_attr($value).'" class="wam-color-picker" style="width:120px;" />';
        if (isset($args['desc'])) echo '<p class="description" style="color:#888;margin:4px 0 0 0;">'.esc_html($args['desc']).'</p>';
        echo '</div>';
    }
    public static function render_text_field($args) {
        $options = get_option('wam_global_settings');
        $value = isset($options[$args['id']]) ? $options[$args['id']] : '';
        $placeholder = isset($args['placeholder']) ? $args['placeholder'] : '';
        echo '<div style="background:#fff;border:1px solid #e0e0e0;border-radius:8px;padding:18px 18px 8px 18px;margin-bottom:18px;box-shadow:0 1px 2px rgba(0,0,0,0.03);max-width:480px;">';
        echo '<label style="font-weight:600;color:#1d2327;display:block;margin-bottom:4px;">'.$args['title'].'</label>';
        echo '<input type="text" name="wam_global_settings['.$args['id'].']" value="'.esc_attr($value).'" placeholder="'.esc_attr($placeholder).'" style="width:220px;" />';
        if (isset($args['desc'])) echo '<p class="description" style="color:#888;margin:4px 0 0 0;">'.esc_html($args['desc']).'</p>';
        echo '</div>';
    }
    public static function render_select_field($args) {
        $options = get_option('wam_global_settings');
        $value = isset($options[$args['id']]) ? $options[$args['id']] : '';
        echo '<div style="background:#fff;border:1px solid #e0e0e0;border-radius:8px;padding:18px 18px 8px 18px;margin-bottom:18px;box-shadow:0 1px 2px rgba(0,0,0,0.03);max-width:480px;">';
        echo '<label style="font-weight:600;color:#1d2327;display:block;margin-bottom:4px;">'.$args['title'].'</label>';
        echo '<select name="wam_global_settings['.$args['id'].']" style="width:120px;">';
        foreach ($args['options'] as $k => $v) {
            echo '<option value="'.$k.'" '.selected($value, $k, false).'>'.$v.'</option>';
        }
        echo '</select>';
        if (isset($args['desc'])) echo '<p class="description" style="color:#888;margin:4px 0 0 0;">'.esc_html($args['desc']).'</p>';
        echo '</div>';
    }

    public static function columns( $columns ) {
        $columns['short_description'] = __( 'Short Description', 'wordpress-advisor-module' );
        $columns['email'] = __( 'Email', 'wordpress-advisor-module' );
        $columns['phone'] = __( 'Phone', 'wordpress-advisor-module' );
        $columns['location'] = __( 'Location', 'wordpress-advisor-module' );
        return $columns;
    }

    public static function column_content( $column, $post_id ) {
        switch ( $column ) {
            case 'short_description':
                echo esc_html( get_post_meta( $post_id, '_wam_short_description', true ) );
                break;
            case 'email':
                echo esc_html( get_post_meta( $post_id, '_wam_email', true ) );
                break;
            case 'phone':
                echo esc_html( get_post_meta( $post_id, '_wam_phone', true ) );
                break;
            case 'location':
                $terms = get_the_terms( $post_id, 'location' );
                if ( $terms && ! is_wp_error( $terms ) ) {
                    $names = wp_list_pluck( $terms, 'name' );
                    echo esc_html( implode( ', ', $names ) );
                }
                break;
        }
    }

    public static function render_shortcode_builder_form($post_id = 0) {
        $post_title = $post_id ? get_the_title($post_id) : '';
        $meta = $post_id ? get_post_meta($post_id) : [];
        $get_meta = function($key, $default = '') use ($meta, $post_id) {
            return $post_id ? get_post_meta($post_id, $key, true) : $default;
        };
        ?>
        <div class="wrap"><h2><?php echo $post_id ? 'Edit Shortcode' : 'Create New Shortcode'; ?></h2>
        <form id="wam-shortcode-builder-form" method="post">
            <input type="hidden" name="post_id" value="<?php echo $post_id; ?>" />
            <table class="form-table">
                <tr><th><label for="post_title">Title</label></th>
                    <td><input type="text" name="post_title" id="post_title" value="<?php echo esc_attr($post_title); ?>" required style="width:100%;" /></td>
                </tr>
                <tr><th><label for="wam_sc_type">Shortcode Type</label></th>
                    <td>
                        <select name="wam_sc_type" id="wam_sc_type">
                            <option value="grid" <?php selected($get_meta('wam_sc_type'), 'grid'); ?>>Grid</option>
                            <option value="masonry" <?php selected($get_meta('wam_sc_type'), 'masonry'); ?>>Masonry</option>
                            <option value="list" <?php selected($get_meta('wam_sc_type'), 'list'); ?>>List</option>
                            <option value="carousel" <?php selected($get_meta('wam_sc_type'), 'carousel'); ?>>Carousel</option>
                        </select>
                    </td>
                </tr>
                <tr><th><label for="wam_sc_columns">Columns</label></th>
                    <td>
                        <select name="wam_sc_columns" id="wam_sc_columns">
                            <option value="2" <?php selected($get_meta('wam_sc_columns'), '2'); ?>>2</option>
                            <option value="3" <?php selected($get_meta('wam_sc_columns'), '3'); ?>>3</option>
                            <option value="4" <?php selected($get_meta('wam_sc_columns'), '4'); ?>>4</option>
                            <option value="5" <?php selected($get_meta('wam_sc_columns'), '5'); ?>>5</option>
                        </select>
                    </td>
                </tr>
                <tr><th><label for="wam_sc_filter">Enable Filter</label></th>
                    <td>
                        <select name="wam_sc_filter" id="wam_sc_filter">
                            <option value="yes" <?php selected($get_meta('wam_sc_filter'), 'yes'); ?>>Yes</option>
                            <option value="no" <?php selected($get_meta('wam_sc_filter'), 'no'); ?>>No</option>
                        </select>
                    </td>
                </tr>
                <tr><th><label for="wam_sc_ppp">Posts Per Page</label></th>
                    <td><input type="number" name="wam_sc_ppp" id="wam_sc_ppp" value="<?php echo esc_attr($get_meta('wam_sc_ppp', '8')); ?>" min="1" style="width:80px;" /></td>
                </tr>

                <tr><th><label for="wam_sc_search">Search Functionality</label></th>
                    <td>
                        <select name="wam_sc_search" id="wam_sc_search">
                            <option value="yes" <?php selected($get_meta('wam_sc_search'), 'yes'); ?>>Yes</option>
                            <option value="no" <?php selected($get_meta('wam_sc_search'), 'no'); ?>>No</option>
                        </select>
                    </td>
                </tr>
                <tr><th><label for="wam_sc_default_role">Default Role (optional)</label></th>
                    <td>
                        <?php
                        $roles = get_terms(['taxonomy' => 'job_role', 'hide_empty' => false]);
                        $selected_role_slug = $get_meta('wam_sc_default_role');
                        $selected_role_name = '';
                        foreach ($roles as $role) {
                            if ($role->slug === $selected_role_slug) {
                                $selected_role_name = $role->name;
                                break;
                            }
                        }
                        ?>
                        <select name="wam_sc_default_role" id="wam_sc_default_role">
                            <option value="">-- None --</option>
                            <?php foreach ($roles as $role): ?>
                                <option value="<?php echo esc_attr($role->slug); ?>" <?php selected($get_meta('wam_sc_default_role'), $role->slug); ?>><?php echo esc_html($role->name); ?></option>
                            <?php endforeach; ?>
                        </select>
                        <div id="wam-default-role-pill" style="margin-top:8px;"></div>
                        <p class="description">If set, the front page will default to this role filter.</p>
                        <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            var select = document.getElementById('wam_sc_default_role');
                            var pillDiv = document.getElementById('wam-default-role-pill');
                            var roleMap = {};
                            <?php foreach ($roles as $role): ?>
                                roleMap['<?php echo esc_js($role->slug); ?>'] = '<?php echo esc_js($role->name); ?>';
                            <?php endforeach; ?>
                            function renderPill(slug) {
                                if (slug && roleMap[slug]) {
                                    pillDiv.innerHTML = '<span class="wam-filter-pill" style="display:inline-flex;align-items:center;background:#ededed;color:#222;padding:4px 14px 4px 12px;border-radius:6px;font-size:15px;margin-bottom:4px;box-shadow:0 1px 2px rgba(0,0,0,0.04);">' +
                                        roleMap[slug] +
                                        '<button type="button" id="wam-remove-default-role" style="background:none;border:none;color:#444;font-size:16px;margin-left:8px;cursor:pointer;line-height:1;">&times;</button>' +
                                    '</span>';
                                    var btn = document.getElementById('wam-remove-default-role');
                                    if (btn) {
                                        btn.addEventListener('click', function() {
                                            select.value = '';
                                            renderPill('');
                                        });
                                    }
                                } else {
                                    pillDiv.innerHTML = '';
                                }
                            }
                            // Initial render
                            renderPill(select.value);
                            // Update on change
                            select.addEventListener('change', function() {
                                renderPill(this.value);
                            });
                        });
                        </script>
                    </td>
                </tr>
                <tr><th><label for="wam_sc_default_locations">Default Locations (optional)</label></th>
                    <td>
                        <?php
                        $locations = get_terms(['taxonomy' => 'location', 'hide_empty' => false]);
                        $selected_locations = $get_meta('wam_sc_default_locations');
                        if (!is_array($selected_locations)) $selected_locations = [];
                        ?>
                        <div style="max-height:200px;overflow-y:auto;border:1px solid #ddd;padding:10px;background:#fafafa;">
                            <label><input type="checkbox" name="wam_sc_default_locations[]" value="all" <?php checked(in_array('all', $selected_locations)); ?> /> <strong>All Locations</strong></label><br><br>
                            <?php foreach ($locations as $location): ?>
                                <label style="display:block;margin-bottom:5px;">
                                    <input type="checkbox" name="wam_sc_default_locations[]" value="<?php echo esc_attr($location->slug); ?>" <?php checked(in_array($location->slug, $selected_locations)); ?> />
                                    <?php echo esc_html($location->name); ?>
                                    <?php if ($location->parent): ?>
                                        <em>(<?php echo esc_html(get_term($location->parent)->name); ?>)</em>
                                    <?php endif; ?>
                                </label>
                            <?php endforeach; ?>
                        </div>
                        <p class="description">Select default locations to filter by on page load.</p>
                    </td>
                </tr>
                <tr><th><label for="wam_sc_default_industries">Default Industries (optional)</label></th>
                    <td>
                        <?php
                        $industries = get_terms(['taxonomy' => 'industry', 'hide_empty' => false]);
                        $selected_industries = $get_meta('wam_sc_default_industries');
                        if (!is_array($selected_industries)) $selected_industries = [];
                        ?>
                        <div style="max-height:200px;overflow-y:auto;border:1px solid #ddd;padding:10px;background:#fafafa;">
                            <label><input type="checkbox" name="wam_sc_default_industries[]" value="all" <?php checked(in_array('all', $selected_industries)); ?> /> <strong>All Industries</strong></label><br><br>
                            <?php foreach ($industries as $industry): ?>
                                <label style="display:block;margin-bottom:5px;">
                                    <input type="checkbox" name="wam_sc_default_industries[]" value="<?php echo esc_attr($industry->slug); ?>" <?php checked(in_array($industry->slug, $selected_industries)); ?> />
                                    <?php echo esc_html($industry->name); ?>
                                    <?php if ($industry->parent): ?>
                                        <em>(<?php echo esc_html(get_term($industry->parent)->name); ?>)</em>
                                    <?php endif; ?>
                                </label>
                            <?php endforeach; ?>
                        </div>
                        <p class="description">Select default industries to filter by on page load.</p>
                    </td>
                </tr>
                <tr><th><label for="wam_sc_default_services">Default Services (optional)</label></th>
                    <td>
                        <?php
                        $services = get_terms(['taxonomy' => 'service', 'hide_empty' => false]);
                        $selected_services = $get_meta('wam_sc_default_services');
                        if (!is_array($selected_services)) $selected_services = [];
                        ?>
                        <div style="max-height:200px;overflow-y:auto;border:1px solid #ddd;padding:10px;background:#fafafa;">
                            <label><input type="checkbox" name="wam_sc_default_services[]" value="all" <?php checked(in_array('all', $selected_services)); ?> /> <strong>All Services</strong></label><br><br>
                            <?php foreach ($services as $service): ?>
                                <label style="display:block;margin-bottom:5px;">
                                    <input type="checkbox" name="wam_sc_default_services[]" value="<?php echo esc_attr($service->slug); ?>" <?php checked(in_array($service->slug, $selected_services)); ?> />
                                    <?php echo esc_html($service->name); ?>
                                    <?php if ($service->parent): ?>
                                        <em>(<?php echo esc_html(get_term($service->parent)->name); ?>)</em>
                                    <?php endif; ?>
                                </label>
                            <?php endforeach; ?>
                        </div>
                        <p class="description">Select default services to filter by on page load.</p>
                    </td>
                </tr>
            </table>
            <p><input type="submit" class="button button-primary" value="<?php echo $post_id ? 'Update Shortcode' : 'Create Shortcode'; ?>" /></p>
        </form></div>
        <?php
    }

    public static function render_settings_page_content() {
        ?>
        <div class="wrap" style="background:#f7f7f7;padding:30px 0 30px 0;">
            <h1 class="sett-pg-ttl">Advisor Module Settings</h1>
            <form method="post" action="options.php">
                <style>
                .wam-settings-grid {
                    display: grid;
                    grid-template-columns: repeat(3, 1fr);
                    gap: 24px;
                    margin-bottom: 32px;
                }
                @media (max-width: 900px) {
                    .wam-settings-grid { grid-template-columns: 1fr 1fr; }
                }
                @media (max-width: 600px) {
                    .wam-settings-grid { grid-template-columns: 1fr; }
                }
                .sett-pg-ttl {
                    margin-bottom: 20px !important;
                    border-bottom: 1px solid #ddd;
                    padding-bottom: 10px !important;
                }
                .wam-settings-card {
                    background: #fff;
                    border: 1px solid #e0e0e0;
                    border-radius: 8px;
                    padding: 18px 18px 8px 18px;
                    box-shadow: 0 1px 2px rgba(0,0,0,0.03);
                }
                </style>
                <?php settings_fields('wam_settings_group'); ?>
                <div class="wam-settings-grid">
                <?php
                global $wp_settings_fields;
                if (isset($wp_settings_fields['wam-settings']['wam_settings_main'])) {
                    foreach ($wp_settings_fields['wam-settings']['wam_settings_main'] as $field) {
                        echo '<div class="wam-settings-card">';
                        call_user_func($field['callback'], $field['args']);
                        echo '</div>';
                    }
                }
                ?>
                </div>
                <?php submit_button(); ?>
            </form>
        </div>
        <?php
    }

    public static function enqueue_settings_scripts($hook) {
        if ( $hook === 'team-members_page_wam-settings' ) {
            wp_enqueue_style( 'wp-color-picker' );
            wp_enqueue_script( 'wp-color-picker' );
            add_action('admin_footer', function() {
                echo '<script>jQuery(document).ready(function($){ $(".wam-color-picker").wpColorPicker(); });</script>';
            });
        }
    }
}

WAM_Admin_UI::init(); 