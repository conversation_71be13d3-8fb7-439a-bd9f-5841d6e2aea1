<?php
/*
Plugin Name: Wordpress Advisor Module
Description: Manage team members, locations, roles, services, and industries with advanced filtering and shortcode support.
Version: 1.1.0
Author: <PERSON>
License: GPL2
Text Domain: wordpress-advisor-module
*/

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Include required files
require_once plugin_dir_path( __FILE__ ) . 'includes/class-cpt.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/class-taxonomies.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/class-db.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/class-meta-fields.php';
require_once plugin_dir_path( __FILE__ ) . 'admin/class-admin-ui.php';

class Wordpress_Advisor_Module {
    public function __construct() {
        // Register custom post types and taxonomies
        add_action( 'init', [ 'WAM_CPT', 'register' ] );
        add_action( 'init', [ 'WAM_Taxonomies', 'register' ] );
        // Register meta boxes
        add_action( 'add_meta_boxes', [ 'WAM_Meta_Fields', 'register_meta_boxes' ] );
        // Save meta box data
        add_action( 'save_post_team_member', [ 'WAM_Meta_Fields', 'save_meta_boxes' ], 10, 2 );
        // Admin UI
        add_action( 'admin_init', [ 'WAM_Admin_UI', 'init' ] );
        // Disable Gutenberg for team_member
        add_filter( 'use_block_editor_for_post_type', [ 'WAM_CPT', 'disable_gutenberg' ], 10, 2 );
        add_action( 'admin_menu', [ $this, 'add_admin_menus' ] );
        // Settings page scripts
        add_action( 'admin_enqueue_scripts', [ $this, 'enqueue_settings_scripts' ] );
        // Allow saving wam_settings_group from custom submenu
        add_filter('option_page_capability_wam_settings_group', function() { return 'manage_options'; });
        // Enqueue frontend CSS only if shortcode is present
        add_action('wp_enqueue_scripts', function() {
            global $post;
            if (is_a($post, 'WP_Post') && has_shortcode($post->post_content, 'advisor_module')) {
                wp_enqueue_style('wam-advisor-frontend', plugin_dir_url(__FILE__).'assets/advisor-module-frontend.css', [], '1.0');
            }
        });
        // Include frontend renderer
        require_once plugin_dir_path(__FILE__).'includes/class-frontend-render.php';
        // Update shortcode handler to use frontend renderer
        add_shortcode('advisor_module', ['WAM_Frontend_Render', 'render_shortcode']);
        
        // AJAX handlers
        add_action('wp_ajax_wam_filter_team_members', [$this, 'ajax_filter_team_members']);
        add_action('wp_ajax_nopriv_wam_filter_team_members', [$this, 'ajax_filter_team_members']);
    }

    public function enqueue_settings_scripts($hook) {
        if ( $hook === 'team-members_page_wam-settings' ) {
            wp_enqueue_style( 'wp-color-picker' );
            wp_enqueue_script( 'wp-color-picker' );
            add_action('admin_footer', function() {
                echo '<script>jQuery(document).ready(function($){ $(".wam-color-picker").wpColorPicker(); });</script>';
            });
        }
    }

    public function add_admin_menus() {
        add_submenu_page(
            'edit.php?post_type=team_member',
            __( 'Shortcode Builder', 'wordpress-advisor-module' ),
            __( 'Shortcode Builder', 'wordpress-advisor-module' ),
            'manage_options',
            'wam-shortcode-builder',
            [ $this, 'render_shortcode_builder_page' ]
        );
        add_submenu_page(
            'edit.php?post_type=team_member',
            __( 'Settings', 'wordpress-advisor-module' ),
            __( 'Settings', 'wordpress-advisor-module' ),
            'manage_options',
            'wam-settings',
            [ $this, 'render_settings_page' ]
        );
    }
    public function render_shortcode_builder_page() {
        $action = isset($_GET['action']) ? $_GET['action'] : 'list';
        if ( $action === 'add' || $action === 'edit' ) {
            $post_id = isset($_GET['post']) ? intval($_GET['post']) : 0;
            if ( $post_id ) {
                $post = get_post($post_id);
                if (!$post || $post->post_type !== 'wam_shortcode') {
                    wp_die('Invalid shortcode.');
                }
            }
            if ( $_SERVER['REQUEST_METHOD'] === 'POST' ) {
                $title = sanitize_text_field($_POST['post_title']);
                $meta = [];
                $fields = ['wam_sc_type', 'wam_sc_columns', 'wam_sc_filter', 'wam_sc_ppp', 'wam_sc_filter_location', 'wam_sc_filter_industry', 'wam_sc_filter_service', 'wam_sc_filter_category', 'wam_sc_filter_job_role', 'wam_sc_search', 'wam_sc_default_role'];
                foreach ( $fields as $field ) {
                    $meta[$field] = isset($_POST[$field]) ? sanitize_text_field($_POST[$field]) : '';
                }
                $post_arr = ['post_title' => $title, 'post_type' => 'wam_shortcode', 'post_status' => 'publish'];
                if ($post_id) $post_arr['ID'] = $post_id;
                $new_post_id = wp_insert_post($post_arr);
                if ($new_post_id) {
                    foreach ( $meta as $key => $val ) {
                        update_post_meta($new_post_id, $key, $val);
                    }
                    echo '<div class="updated"><p>Shortcode saved!</p></div>';
                }
                echo '<script>window.location.href="?post_type=team_member&page=wam-shortcode-builder";</script>';
            }
            WAM_Admin_UI::render_shortcode_builder_form($post_id);
        } else {
            ?>
            <div class="wrap">
                <h1 class="wp-heading-inline">Shortcodes</h1>
                <a href="?post_type=team_member&page=wam-shortcode-builder&action=add" class="page-title-action">Add New Shortcode</a>
                <hr class="wp-header-end">
                <table class="fixed wp-list-table widefat striped">
                    <thead>
                        <tr><th style="width:30%">Title</th><th>Type</th><th>Shortcode</th><th>Date</th></tr>
                    </thead>
                    <tbody>
                    <?php
                    $shortcodes = get_posts(['post_type' => 'wam_shortcode', 'posts_per_page' => -1]);
                    if (empty($shortcodes)) {
                        echo '<tr><td colspan="4">No Shortcode found</td></tr>';
                    } else {
                        foreach ($shortcodes as $sc) {
                            $type = get_post_meta($sc->ID, 'wam_sc_type', true);
                            echo '<tr>';
                            echo '<td><strong><a href="?post_type=team_member&page=wam-shortcode-builder&action=edit&post='.$sc->ID.'">'.esc_html($sc->post_title).'</a></strong>';
                            echo '<div class="row-actions"><a href="?post_type=team_member&page=wam-shortcode-builder&action=edit&post='.$sc->ID.'">Edit</a> | <a href="'.get_delete_post_link($sc->ID).'" onclick="return confirm(\'Are you sure?\')">Delete</a></div></td>';
                            echo '<td>'.esc_html(ucfirst($type)).'</td>';
                            echo '<td><code>[advisor_module id="'.$sc->ID.'"]</code></td>';
                            echo '<td>'.get_the_date('Y/m/d', $sc->ID).'</td>';
                            echo '</tr>';
                        }
                    }
                    ?>
                    </tbody>
                </table>
            </div>
            <?php
        }
    }
    public function render_settings_page() {
        if ( class_exists('WAM_Admin_UI') ) {
            WAM_Admin_UI::render_settings_page_content();
        }
    }

    public function ajax_filter_team_members() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'wam_filter_nonce')) {
                error_log('WAM AJAX: Security check failed. Nonce: ' . print_r($_POST['nonce'], true));
                wp_send_json_error('Security check failed');
            }
            $shortcode_id = intval($_POST['shortcode_id']);
            $search = sanitize_text_field($_POST['search']);
            $filters = isset($_POST['filters']) ? json_decode(stripslashes($_POST['filters']), true) : [];
            error_log('WAM AJAX: Received filters: ' . print_r($filters, true));
            if (!is_array($filters)) {
                $filters = [];
            }
            if (!$shortcode_id) {
                error_log('WAM AJAX: Invalid shortcode ID');
                wp_send_json_error('Invalid shortcode ID');
            }
            $shortcode = get_post($shortcode_id);
            if (!$shortcode || $shortcode->post_type !== 'wam_shortcode') {
                error_log('WAM AJAX: Invalid shortcode');
                wp_send_json_error('Invalid shortcode');
            }
            $meta = get_post_meta($shortcode_id);
            $layout = isset($meta['wam_sc_type'][0]) ? $meta['wam_sc_type'][0] : 'grid';
            $columns = isset($meta['wam_sc_columns'][0]) ? intval($meta['wam_sc_columns'][0]) : 3;
            $posts_per_page = isset($meta['wam_sc_ppp'][0]) ? intval($meta['wam_sc_ppp'][0]) : 12;
            // Build query args
            $args = [
                'post_type' => 'team_member',
                'post_status' => 'publish',
                'posts_per_page' => $posts_per_page,
                'orderby' => 'title',
                'order' => 'ASC'
            ];
            // Add search
            if (!empty($search)) {
                $args['s'] = $search;
            }
            // Add taxonomy filters
            $tax_query = [];
            foreach ($filters as $taxonomy => $term_slugs) {
                if (is_array($term_slugs)) {
                    $term_slugs = array_filter($term_slugs); // Remove empty values
                    if (!empty($term_slugs)) {
                        $tax_query[] = [
                            'taxonomy' => $taxonomy,
                            'field' => 'slug',
                            'terms' => $term_slugs
                        ];
                    }
                } elseif (!empty($term_slugs)) {
                    $tax_query[] = [
                        'taxonomy' => $taxonomy,
                        'field' => 'slug',
                        'terms' => [$term_slugs]
                    ];
                }
            }
            if (!empty($tax_query)) {
                $args['tax_query'] = $tax_query;
            }
            error_log('WAM AJAX: WP_Query args: ' . print_r($args, true));
            $query = new WP_Query($args);
            ob_start();
            WAM_Frontend_Render::render_team_cards($query, $layout);
            $html = ob_get_clean();
            wp_reset_postdata();
            wp_send_json_success(['html' => $html]);
        } catch (Throwable $e) {
            error_log('WAM AJAX: PHP Error: ' . $e->getMessage() . ' on line ' . $e->getLine() . ' in ' . $e->getFile());
            wp_send_json_error('PHP Error: ' . $e->getMessage() . ' on line ' . $e->getLine() . ' in ' . $e->getFile());
        }
    }
}

// Add this at the end of the main plugin file
// add_action('init', function() {
//     add_rewrite_rule('^team-member/([0-9a-zA-Z-_]+)/?$', 'index.php?post_type=team_member&name=$1', 'top');
// });

add_filter('template_include', function($template) {
    if (is_singular('team_member')) {
        return plugin_dir_path(__FILE__).'single-team-member-template.php';
    }
    return $template;
});

add_shortcode('wam_single_team_member', function($atts) {
    $atts = shortcode_atts(['id' => get_the_ID()], $atts);
    if (!class_exists('WAM_Frontend_Render')) return '';
    return WAM_Frontend_Render::render_single_team_member_page($atts['id']);
});

// Activation hook for DB table
register_activation_hook( __FILE__, [ 'WAM_DB', 'install' ] );

// Initialize the plugin
new Wordpress_Advisor_Module(); 