<?php
if ( ! defined( 'ABSPATH' ) ) exit;

class WAM_Frontend_Render {
    public static function render_shortcode($atts) {
        $atts = shortcode_atts(['id' => ''], $atts);
        $shortcode_id = intval($atts['id']);
        
        if (!$shortcode_id) return '<p>Error: Shortcode ID is required.</p>';
        
        $shortcode = get_post($shortcode_id);
        if (!$shortcode || $shortcode->post_type !== 'wam_shortcode') {
            return '<p>Error: Invalid shortcode ID.</p>';
        }
        
        $meta = get_post_meta($shortcode_id);
        $layout = isset($meta['wam_sc_type'][0]) ? $meta['wam_sc_type'][0] : 'grid';
        $columns = isset($meta['wam_sc_columns'][0]) ? intval($meta['wam_sc_columns'][0]) : 3;
        $posts_per_page = isset($meta['wam_sc_ppp'][0]) ? intval($meta['wam_sc_ppp'][0]) : 12;
        $default_role = isset($meta['wam_sc_default_role'][0]) ? $meta['wam_sc_default_role'][0] : '';
        $default_role_label = '';
        if ($default_role) {
            $role_term = get_term_by('slug', $default_role, 'job_role');
            if ($role_term && !is_wp_error($role_term)) {
                $default_role_label = $role_term->name;
            } else {
                $default_role_label = $default_role;
            }
        }
        
        // Get global settings
        $global_settings = get_option('wam_global_settings', []);
        $main_color = isset($global_settings['main_color']) ? $global_settings['main_color'] : '#0073aa';
        $font_family = isset($global_settings['font_family']) ? $global_settings['font_family'] : 'Arial, sans-serif';
        $card_style = isset($global_settings['card_style']) ? $global_settings['card_style'] : 'shadow';
        
        // Start output buffering
        ob_start();
        
        echo '<div class="wam-advisor-module" style="--wam-main-color: '.esc_attr($main_color).'; font-family: '.esc_attr($font_family).';">';
        
        // Output default role as JS variable for this shortcode
        if ($default_role) {
            echo "<script>window.wamDefaultRole_{$shortcode_id} = {slug: '".esc_js($default_role)."', label: '".esc_js($default_role_label)."'};</script>";
        }

        // Render filters
        self::render_filters($shortcode_id);
        
        // Team grid container with data attributes for AJAX
        $grid_class = 'wam-team-grid';
        $grid_style = '';
        $card_width = '';
        if ($layout === 'grid') {
            $grid_style = 'display:grid;grid-template-columns:repeat('.$columns.',1fr);gap:24px;';
        } elseif ($layout === 'masonry') {
            $grid_style = 'columns:'.$columns.';column-gap:24px;';
        } elseif ($layout === 'list') {
            $grid_style = 'display:flex;flex-direction:column;gap:16px;';
        } elseif ($layout === 'carousel') {
            // Force 2 columns for carousel
            $columns = 2;
            $grid_style = 'display:flex;overflow-x:auto;gap:24px;padding:16px 0;';
            $card_width = 'style="flex:0 0 calc(50% - 24px);max-width:calc(50% - 24px);"';
            echo '<div class="wam-carousel-nav" style="text-align:center;margin-top:16px;">';
            echo '<button class="wam-prev" style="margin-right:8px;padding:8px 16px;background:var(--wam-main-color);color:white;border:none;border-radius:4px;cursor:pointer;">← Previous</button>';
            echo '<button class="wam-next" style="margin-left:8px;padding:8px 16px;background:var(--wam-main-color);color:white;border:none;border-radius:4px;cursor:pointer;">Next →</button>';
            echo '</div>';
            ?>
            <script>
            jQuery(function($){
                var $carousel = $(".wam-team-grid[data-shortcode-id='<?php echo $shortcode_id; ?>']");
                var scrollAmount = $carousel.width();
                var autoScrollInterval;
                function scrollNext() {
                    $carousel.animate({scrollLeft: $carousel.scrollLeft() + scrollAmount}, 400);
                }
                function scrollPrev() {
                    $carousel.animate({scrollLeft: $carousel.scrollLeft() - scrollAmount}, 400);
                }
                $(".wam-prev").on("click", function(e) {
                    e.preventDefault();
                    scrollPrev();
                });
                $(".wam-next").on("click", function(e) {
                    e.preventDefault();
                    scrollNext();
                });
                function startAutoScroll() {
                    autoScrollInterval = setInterval(scrollNext, 3000);
                }
                function stopAutoScroll() {
                    clearInterval(autoScrollInterval);
                }
                $carousel.on('mouseenter', stopAutoScroll);
                $carousel.on('mouseleave', startAutoScroll);
                startAutoScroll();
            });
            </script>
            <?php
        }
        
        echo '<div class="'.$grid_class.'" data-shortcode-id="'.$shortcode_id.'" style="'.$grid_style.'">';
        
        // Initial team members query
        $args = [
            'post_type' => 'team_member',
            'post_status' => 'publish',
            'posts_per_page' => $posts_per_page,
            'orderby' => 'title',
            'order' => 'ASC'
        ];
        if ($default_role) {
            $args['tax_query'] = [
                [
                    'taxonomy' => 'job_role',
                    'field' => 'slug',
                    'terms' => $default_role
                ]
            ];
        }
        
        $query = new WP_Query($args);
        self::render_team_cards($query, $layout, $card_width);
        wp_reset_postdata();
        
        // Hide bottom scrollbar for carousel
        if ($layout === 'carousel') {
            echo '<style>
            .wam-team-grid[data-shortcode-id="'.$shortcode_id.'"] {
                scrollbar-width: none; /* Firefox */
                -ms-overflow-style: none; /* IE 10+ */
            }
            .wam-team-grid[data-shortcode-id="'.$shortcode_id.'"]::-webkit-scrollbar {
                display: none; /* Chrome/Safari/Webkit */
            }
            </style>';
        }
        
        echo '</div>';
        
        echo '</div>';
        
        return ob_get_clean();
    }

    public static function render_team_cards($query, $mode = 'grid', $card_width = '') {
        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $img = get_the_post_thumbnail_url(get_the_ID(), 'medium');
                $secondary_id = get_post_meta(get_the_ID(), '_wam_secondary_image_id', true);
                $secondary_img = $secondary_id ? wp_get_attachment_image_url($secondary_id, 'medium') : '';
                $role = '';
                $roles = get_the_terms(get_the_ID(), 'job_role');
                if ($roles && !is_wp_error($roles)) {
                    $role = esc_html($roles[0]->name);
                }
                $card_class = 'wam-team-card';
                if ($mode === 'carousel') $card_class .= ' swiper-slide';
                $card_style = ($mode === 'carousel' && $card_width) ? ' '.$card_width : '';
                // Link logic
                $custom_link = get_post_meta(get_the_ID(), 'wam_custom_link', true);
                $disable_link = get_post_meta(get_the_ID(), 'wam_disable_link', true);
                $single_link = get_permalink();
                $final_link = '';
                $link_target = '';
                if (!$disable_link) {
                    if (!empty($custom_link)) {
                        $final_link = esc_url($custom_link);
                        $link_target = ' target="_blank" rel="noopener"';
                    } else {
                        $final_link = esc_url($single_link);
                        $link_target = '';
                    }
                }
                // Social meta
                $phone = get_post_meta(get_the_ID(), 'wam_phone', true);
                $linkedin = get_post_meta(get_the_ID(), '_wam_linkedin', true);
                $facebook = get_post_meta(get_the_ID(), '_wam_facebook', true);
                $instagram = get_post_meta(get_the_ID(), '_wam_instagram', true);
                $email = get_post_meta(get_the_ID(), 'wam_email', true);
                // Custom social links (repeater)
                $custom_socials = get_post_meta(get_the_ID(), '_wam_custom_socials', true);
                if (!is_array($custom_socials)) $custom_socials = [];
                // Check if any icon class is present to enqueue Font Awesome
                $fa_needed = false;
                foreach ($custom_socials as $row) {
                    if (!empty($row['icon'])) {
                        $fa_needed = true;
                        break;
                    }
                }
                if ($fa_needed && !wp_style_is('font-awesome', 'enqueued')) {
                    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css');
                }
                $has_social = $phone || $linkedin || $facebook || $instagram || $email || count($custom_socials);
                
                $social_html = '';
                if ($has_social) {
                    $social_html = '<div class="wam-social-row" style="display:flex;gap:12px;justify-content:center;margin-top:14px;">';
                    if ($phone) {
                        $social_html .= '<a href="tel:'.esc_attr($phone).'" class="wam-social-icon" style="display:inline-flex;align-items:center;justify-content:center;width:38px;height:38px;background:#21738a;color:#fff;border-radius:6px;font-size:20px;" title="Call"><i class="fas fa-phone"></i></a>';
                    }
                    if ($email) {
                        $social_html .= '<a href="mailto:'.esc_attr($email).'" class="wam-social-icon" style="display:inline-flex;align-items:center;justify-content:center;width:38px;height:38px;background:#21738a;color:#fff;border-radius:6px;font-size:20px;" title="Email"><i class="fas fa-envelope"></i></a>';
                    }
                    if ($facebook) {
                        $social_html .= '<a href="'.esc_url($facebook).'" class="wam-social-icon" style="display:inline-flex;align-items:center;justify-content:center;width:38px;height:38px;background:#21738a;color:#fff;border-radius:6px;font-size:20px;" target="_blank" rel="noopener" title="Facebook"><i class="fab fa-facebook-f"></i></a>';
                    }
                    if ($instagram) {
                        $social_html .= '<a href="'.esc_url($instagram).'" class="wam-social-icon" style="display:inline-flex;align-items:center;justify-content:center;width:38px;height:38px;background:#21738a;color:#fff;border-radius:6px;font-size:20px;" target="_blank" rel="noopener" title="Instagram"><i class="fab fa-instagram"></i></a>';
                    }
                    if ($linkedin) {
                        $social_html .= '<a href="'.esc_url($linkedin).'" class="wam-social-icon" style="display:inline-flex;align-items:center;justify-content:center;width:38px;height:38px;background:#21738a;color:#fff;border-radius:6px;font-size:20px;" target="_blank" rel="noopener" title="LinkedIn"><i class="fab fa-linkedin-in"></i></a>';
                    }

                    // Render custom socials
                    foreach ($custom_socials as $row) {
                        $name = isset($row['name']) ? esc_attr($row['name']) : 'Social';
                        $url = isset($row['url']) ? esc_url($row['url']) : '';
                        $icon = isset($row['icon']) ? esc_attr($row['icon']) : '';
                        $icon_img_id = isset($row['icon_img_id']) ? intval($row['icon_img_id']) : '';
                        $icon_img_url = $icon_img_id ? wp_get_attachment_image_url($icon_img_id, 'thumbnail') : '';
                        if ($url) {
                            $social_html .= '<a href="'.$url.'" class="wam-social-icon" style="display:inline-flex;align-items:center;justify-content:center;width:38px;height:38px;background:#21738a;color:#fff;border-radius:6px;font-size:20px;" target="_blank" rel="noopener" title="'.$name.'">';
                            if ($icon_img_url) {
                                $social_html .= '<img src="'.$icon_img_url.'" alt="'.$name.'" style="width:20px;height:20px;object-fit:contain;display:block;">';
                            } elseif ($icon) {
                                $social_html .= '<i class="'.$icon.'"></i>';
                            } elseif (stripos($name, 'linkedin') !== false) {
                                $social_html .= '<i class="fab fa-linkedin-in"></i>';
                            } elseif (stripos($name, 'facebook') !== false) {
                                $social_html .= '<i class="fab fa-facebook-f"></i>';
                            } elseif (stripos($name, 'instagram') !== false) {
                                $social_html .= '<i class="fab fa-instagram"></i>';
                            } else {
                                $social_html .= '<span class="dashicons dashicons-share"></span>';
                            }
                            $social_html .= '</a>';
                        }
                    }
                    $social_html .= '</div>';
                }
                // --- List layout: image left, content right ---
                if ($mode === 'list') {
                    echo '<div class="wam-list-card '.$card_class.'" style="display:flex;align-items:center;gap:24px;padding:18px 24px;background:#fff;border-radius:8px;border:1px solid #eee;">';
                    if ($img) {
                        echo '<div class="wam-team-img-wrap" style="flex-shrink:0;width:140px;height:140px;display:flex;align-items:center;justify-content:center;position:relative;">';
                        if ($final_link) {
                            echo '<a href="'.$final_link.'"'.$link_target.' style="display:block;width:140px;height:140px;">';
                        }
                        echo '<img class="wam-team-img-main" src="'.esc_url($img).'" alt="'.esc_attr(get_the_title()).'" >';
                        if ($secondary_img) {
                            echo '<img class="wam-team-img-secondary" src="'.esc_url($secondary_img).'" alt="'.esc_attr(get_the_title()).' secondary" style="width:80px;height:80px;object-fit:cover;border-radius:50%;box-shadow:0 2px 8px rgba(0,0,0,0.08);position:absolute;top:0;left:0;opacity:0;transition:opacity 0.3s;">';
                        }
                        if ($final_link) {
                            echo '</a>';
                        }
                        echo '</div>';
                    }
                    echo '<div class="wam-list-content" style="flex:1;min-width:0;">';
                    echo '<div style="font-size:1.2em;font-weight:600;margin-bottom:4px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">';
                    if ($final_link) {
                        echo '<a href="'.$final_link.'"'.$link_target.' style="color:inherit;text-decoration:none;">'.esc_html(get_the_title()).'</a>';
                    } else {
                        echo esc_html(get_the_title());
                    }
                    echo '</div>';
                    if ($role) echo '<div style="font-size:1em;color:var(--wam-main-color);margin-bottom:8px;">'.$role.'</div>';
                    // Optionally add more info here
                    echo $social_html;
                    echo '</div>';
                    echo '</div>';
                } else if ($mode === 'masonry') {
                    echo '<div class="'.$card_class.' wam-masonry-card">';
                    if ($img) {
                        echo '<div class="wam-team-img-wrap" style="margin-bottom:16px;position:relative;display:flex;align-items:center;justify-content:center;margin-left:auto;margin-right:auto;">';
                        if ($final_link) {
                            echo '<a href="'.$final_link.'"'.$link_target.' style="display:block;width:140px;height:140px;">';
                        }
                        echo '<img class="wam-team-img-main" src="'.esc_url($img).'" alt="'.esc_attr(get_the_title()).'" >';
                        if ($secondary_img) {
                            echo '<img class="wam-team-img-secondary" src="'.esc_url($secondary_img).'" alt="'.esc_attr(get_the_title()).' secondary" >';
                        }
                        if ($final_link) {
                            echo '</a>';
                        }
                        echo '</div>';
                    }
                    echo '<div style="font-size:1.15em;font-weight:600;margin-bottom:4px;text-align:center;">';
                    if ($final_link) {
                        echo '<a href="'.$final_link.'"'.$link_target.' style="color:inherit;text-decoration:none;">'.esc_html(get_the_title()).'</a>';
                    } else {
                        echo esc_html(get_the_title());
                    }
                    echo '</div>';
                    if ($role) echo '<div style="font-size:1em;color:var(--wam-main-color);margin-bottom:8px;text-align:center;">'.$role.'</div>';
                    echo $social_html;
                    echo '</div>';
                } else {
                    echo '<div class="'.$card_class.'"'.$card_style.'>';
                    if ($img) {
                        echo '<div class="wam-team-img-wrap" style="margin-bottom:18px;position:relative;width:140px;height:140px;display:flex;align-items:center;justify-content:center;margin-left:auto;margin-right:auto;">';
                        if ($final_link) {
                            echo '<a href="'.$final_link.'"'.$link_target.' style="display:block;width:96px;height:96px;">';
                        }
                        echo '<img class="wam-team-img-main" src="'.esc_url($img).'" alt="'.esc_attr(get_the_title()).'" >';
                        if ($secondary_img) {
                            echo '<img class="wam-team-img-secondary" src="'.esc_url($secondary_img).'" alt="'.esc_attr(get_the_title()).' secondary" >';
                        }
                        if ($final_link) {
                            echo '</a>';
                        }
                        echo '</div>';
                    }
                    echo '<div style="font-size:1.2em;font-weight:600;margin-bottom:4px;">';
                    if ($final_link) {
                        echo '<a href="'.$final_link.'"'.$link_target.' style="color:inherit;text-decoration:none;">'.esc_html(get_the_title()).'</a>';
                    } else {
                        echo esc_html(get_the_title());
                    }
                    echo '</div>';
                    if ($role) echo '<div style="font-size:1em;color:var(--wam-main-color);margin-bottom:8px;">'.$role.'</div>';
                    echo $social_html;
                    echo '</div>';
                }
            }
        } else {
            echo '<div style="grid-column:1/-1;text-align:center;color:#b00;font-size:1.1em;padding:32px 0;">No team members found matching your criteria.<br>Please adjust your filters or search.</div>';
        }
        echo '<style>
        .wam-team-img-wrap{position:relative;}.wam-team-img-secondary{pointer-events:none;}
        .wam-list-card:hover {box-shadow:0 4px 10px rgba(0,0,0,0.10);border-color:#ddd;}
        @media (max-width: 600px) {
            .wam-list-card {flex-direction:column;align-items:flex-start;gap:12px;padding:14px 10px;}
            .wam-list-card .wam-team-img-wrap {width:64px;height:64px;}
        }
        .wam-team-grid[data-shortcode-id] {transition:all 0.2s;}
        .wam-masonry-card {margin-bottom:24px;}
        @media (max-width: 900px) {
            .wam-team-grid[data-shortcode-id][style*="columns"] {grid-template-columns: repeat(2, 1fr) !important;}
        }
        @media (max-width: 600px) {
            .wam-team-grid[data-shortcode-id][style*="columns"] {grid-template-columns: repeat(1, 1fr) !important;}
        }
        .wam-social-row .dashicons, .wam-social-row i {font-size:20px;line-height:1;}
        .wam-social-row img {width:20px;height:20px;object-fit:contain;display:block;}
        .wam-social-icon:hover {background:#15596a;color:#fff;}
        </style>';
        echo '<script>jQuery(function($){
            $(document).on("mouseenter", ".wam-team-img-wrap", function(){
                var $secondary = $(this).find(".wam-team-img-secondary");
                if ($secondary.length) {
                    $secondary.css("opacity",1);
                    $(this).find(".wam-team-img-main").css("opacity",0);
                }
            });
            $(document).on("mouseleave", ".wam-team-img-wrap", function(){
                var $secondary = $(this).find(".wam-team-img-secondary");
                if ($secondary.length) {
                    $secondary.css("opacity",0);
                    $(this).find(".wam-team-img-main").css("opacity",1);
                }
            });
        });</script>';
    }

    public static function render_single_team_member_page($post_id = null) {
        if (!$post_id) {
            global $post;
            $post_id = $post ? $post->ID : 0;
        }
        if (!$post_id) return '<p>Team member not found.</p>';
        $post = get_post($post_id);
        if (!$post || $post->post_type !== 'team_member') return '<p>Invalid team member.</p>';
        $name = get_the_title($post_id);
        $role = '';
        $roles = get_the_terms($post_id, 'job_role');
        if ($roles && !is_wp_error($roles)) {
            $role = esc_html($roles[0]->name);
        }
        $title = get_post_meta($post_id, 'wam_title', true);
        $sort_desc = get_post_meta($post_id, '_wam_sort_desc', true);
        $email = get_post_meta($post_id, '_wam_email', true);
        $phone = get_post_meta($post_id, '_wam_phone', true);
        $linkedin = get_post_meta($post_id, '_wam_linkedin', true);
        $facebook = get_post_meta($post_id, '_wam_facebook', true);
        $instagram = get_post_meta($post_id, '_wam_instagram', true);
        $contact_link_url = get_post_meta($post_id, '_wam_custom_link', true);
        $designation_heading = get_post_meta($post_id, '_wam_designation_heading', true);
        $designation_content = get_post_meta($post_id, '_wam_designation_content', true);

        // Custom social links (repeater)
        $custom_socials = get_post_meta($post_id, '_wam_custom_socials', true);
        if (!is_array($custom_socials)) $custom_socials = [];

        // Check if any social media links exist
        $has_social = $phone || $linkedin || $facebook || $instagram || $email || count($custom_socials);

        // Debug output
        echo '<!-- Debug: 
        Sort Desc: ' . esc_html($sort_desc) . '
        Designation Heading: ' . esc_html($designation_heading) . '
        Designation Content: ' . esc_html($designation_content) . '
        Phone: ' . esc_html($phone) . '
        Email: ' . esc_html($email) . '
        -->';
        if (empty($contact_link_url)) {
            $contact_link_url = '/contact';
        }
        $img = get_the_post_thumbnail_url($post_id, 'large');
        $content = apply_filters('the_content', $post->post_content);
        // Custom info sections
        $custom_infos = get_post_meta($post_id, '_wam_custom_infos', true);
        if (!is_array($custom_infos)) $custom_infos = [];
        $fa_needed = false;
        foreach ($custom_socials as $row) {
            if (!empty($row['icon'])) { $fa_needed = true; break; }
        }
        if ($fa_needed && !wp_style_is('font-awesome', 'enqueued')) {
            wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css');
        }
        ob_start();
        ?>
        <style>
			h2.wam-single-team-name-h2 {margin-top: 0;}
			h3.wam-single-team-sidebar-title-h3 {margin: 0; padding-bottom: 20px;}
			.wam-single-team-sidebar-box .wam-social-row {display: none !important;}
        .wam-single-team-container { display: flex; flex-wrap: wrap; gap: 40px; align-items: flex-start; max-width: 1400px; width:90%; margin: 80px auto; }
        .wam-single-team-main { flex: 2 1 500px; min-width: 320px; }
        .wam-single-team-header { display: flex; gap: 28px; align-items: center; padding-bottom: 24px; border-bottom: 1px solid #e0e0e0; margin-bottom: 24px; }
        .wam-single-team-img { width: 200px; height: 200px; object-fit: cover; border-radius: 50%; box-shadow: 0 4px 16px rgba(0,0,0,0.10); }
        .wam-single-team-header-content { flex: 1; }
        .wam-single-team-name { font-size: 2.2em; font-weight: 700; color: #e76b1c; line-height: 1.1; margin: 0 0 4px 0; }
        .wam-single-team-credentials { font-size: 1.2em; font-weight: 600; margin-bottom: 4px; color: #333; }
        .wam-single-team-title { font-size: 1.1em; color: #444; margin-bottom: 16px; }
        .wam-single-team-designation-heading { font-size: 1.2em; font-weight: 600; color: #333; margin: 16px 0 4px 0; }
        .wam-single-team-designation-content { font-size: 1.1em; color: #444; line-height: 1.5; margin-bottom: 16px; font-weight:600;}
        .wam-single-team-contact-row { display: flex; gap: 10px; align-items: center; flex-wrap: wrap; }
        .wam-single-team-contact-row a { color: #333; text-decoration: none; display: flex; align-items: center; gap: 8px; font-size: 0.85em; }
        .wam-single-team-contact-row a:hover { color: #e76b1c; }
        .wam-contact-icon { fill: #555; width: 18px; height: 18px; }
        .wam-single-team-desc { font-size: 1.2em; margin: 24px 0 18px 0; line-height: 1.5; color: #222; font-weight: bold; }
        .wam-single-team-content { margin-bottom: 24px; }
        .wam-single-team-section { margin-bottom: 18px; }
        .wam-single-team-section-title { font-size: 1.3em; font-weight: 700; margin-bottom: 6px; }
        .wam-single-team-section-content { font-size: 1.08em; line-height: 1.6; }
        .wam-single-team-sidebar { flex: 1 1 300px; min-width: 260px; }
        .wam-single-team-sidebar-box { background: #fff; border-radius: 10px; border: 1px solid #efefef; padding: 28px 18px 18px 18px; margin-bottom: 24px; }
        .wam-single-team-sidebar-title { font-size: 1.5em; font-weight: 700; margin-bottom: 18px; }
        .wam-single-team-sidebar-box .wam-single-team-sidebar-btn:hover { background: #E4701E !important; color: #fff !important;}
        .wam-single-team-sidebar-btn { display: block; background: #006580; color: #fff; font-weight: 400; text-align: center; padding: 17px 17px; border-radius: 6px; text-decoration: none; margin-bottom: 18px; font-size:16px; line-height:normal; }
        .wam-single-team-sidebar-quote { 
            font-size: 1.08em; 
            color: #444; 
            line-height: 1.5; 
            /* background: #fafbfc;  */
            padding: 24px 20px; 
           border-bottom: 1px solid #efefef;
            margin-top: 10px;
            position: relative;
        }
       
        .wam-single-team-sidebar-quote-text {
            position: relative;
            z-index: 1;
            font-style: italic;
        }
        @media (max-width: 900px) { .wam-single-team-container { flex-direction: column; gap: 24px; margin: 40px auto;  } }
			@media (max-width: 767px){
				.wam-single-team-header {display: block;}
				.wam-single-team-header .wam-single-team-header-content {margin-top: 30px;}
				.wam-single-team-sidebar{width:100% !important;}
			}	
        </style>
        <div class="wam-single-team-container">
            <div class="wam-single-team-main">
                <div class="wam-single-team-header">
                    <?php if ($img): ?>
                        <img src="<?php echo esc_url($img); ?>" alt="<?php echo esc_attr($name); ?>" class="wam-single-team-img">
                    <?php endif; ?>
                    <div class="wam-single-team-header-content">
                        <h2 class="wam-single-team-name-h2"><?php echo esc_html($name); ?></h2>
                        <!-- <?php if ($role): ?><div class="wam-single-team-credentials"><?php echo $role; ?></div><?php endif; ?> -->
                        <?php if ($title): ?><div class="wam-single-team-title"><?php echo esc_html($title); ?></div><?php endif; ?>
                        
                        <!-- Designation Section -->
                        <?php if (!empty($designation_heading) || !empty($designation_content)): ?>
                            <div class="wam-single-team-designation">
                                <?php if (!empty($designation_heading)): ?>
                                    <div class="wam-single-team-designation-heading"><?php echo esc_html($designation_heading); ?></div>
                                <?php endif; ?>
                                <?php if (!empty($designation_content)): ?>
                                    <div class="wam-single-team-designation-content"><?php echo esc_html($designation_content); ?></div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        <div class="wam-single-team-contact-row">
                            <?php if ($phone): ?>
                                <a href="tel:<?php echo esc_attr($phone); ?>">
                                    <i class="fas fa-phone" style="color:#21738a;font-size:16px;"></i>
                                    <span><?php echo esc_html($phone); ?></span>
                                </a>
                            <?php endif; ?>
                            <?php if ($email): ?>
                                <a href="mailto:<?php echo esc_attr($email); ?>" >
                                    <i class="fas fa-envelope" style="color:#21738a;font-size:16px;"></i>
                                    <span><?php echo esc_html($email); ?></span>
                                </a>
                            <?php endif; ?>
                            <?php if ($linkedin): ?>
                                <a href="<?php echo esc_url($linkedin); ?>" target="_blank" rel="noopener" >
                                    <i class="fab fa-linkedin-in" style="color:#21738a;font-size:16px;"></i>
                                    <span>LinkedIn</span>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="wam-single-team-content"> <?php echo $content; ?> </div>
                <?php
                // Custom info sections
                foreach ($custom_infos as $info) {
                    $label = isset($info['label']) ? esc_html($info['label']) : '';
                    $content = isset($info['content']) ? wp_kses_post($info['content']) : '';
                    if ($label && $content) {
                        echo '<div class="wam-single-team-section">';
                        echo '<div class="wam-single-team-section-title">'.esc_html($label).'</div>';
                        echo '<div class="wam-single-team-section-content">'.$content.'</div>';
                        echo '</div>';
                    }
                }
                ?>
            </div>
            <div class="wam-single-team-sidebar">
                <div class="wam-single-team-sidebar-box">
                    <h3 class="wam-single-team-sidebar-title-h3">GET IN TOUCH</h3>
                    <a href="<?php echo esc_url($contact_link_url); ?>" class="wam-single-team-sidebar-btn">Contact <?php echo esc_html($name); ?></a>
                    <?php if ($sort_desc): ?>
                        <div class="wam-single-team-sidebar-quote">
                            <div class="wam-single-team-sidebar-quote-text">
                                "<?php echo esc_html(trim($sort_desc, '"')); ?>"
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($has_social): ?>
                    <div class="wam-social-row" style="display:flex;gap:12px;justify-content:center;margin:20px 0;">
                        <?php if ($phone): ?>
                                <a href="tel:<?php echo esc_attr($phone); ?>">
                                    <i class="fas fa-phone" style="color:#21738a;font-size:16px;"></i>
                                    <span><?php echo esc_html($phone); ?></span>
                                </a>
                            <?php endif; ?>
                            <?php if ($email): ?>
                                <a href="mailto:<?php echo esc_attr($email); ?>" >
                                    <i class="fas fa-envelope" style="color:#21738a;font-size:16px;"></i>
                                    <span><?php echo esc_html($email); ?></span>
                                </a>
                            <?php endif; ?>
                            <?php if ($linkedin): ?>
                                <a href="<?php echo esc_url($linkedin); ?>" target="_blank" rel="noopener" >
                                    <i class="fab fa-linkedin-in" style="color:#21738a;font-size:16px;"></i>
                                    <span>LinkedIn</span>
                                </a>
                            <?php endif; ?>

                        <?php if ($facebook): ?>
                            <a href="<?php echo esc_url($facebook); ?>" class="wam-social-icon" style="display:inline-flex;align-items:center;justify-content:center;width:38px;height:38px;background:#21738a;color:#fff;border-radius:6px;font-size:20px;" target="_blank" rel="noopener" title="Facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                        <?php endif; ?>
                        <?php if ($instagram): ?>
                            <a href="<?php echo esc_url($instagram); ?>" class="wam-social-icon" style="display:inline-flex;align-items:center;justify-content:center;width:38px;height:38px;background:#21738a;color:#fff;border-radius:6px;font-size:20px;" target="_blank" rel="noopener" title="Instagram">
                                <i class="fab fa-instagram"></i>
                            </a>
                        <?php endif; ?>
                       
                      
                        <?php 
                        // Render custom socials
                        foreach ($custom_socials as $row) {
                            $name = isset($row['name']) ? esc_attr($row['name']) : 'Social';
                            $url = isset($row['url']) ? esc_url($row['url']) : '';
                            $icon = isset($row['icon']) ? esc_attr($row['icon']) : '';
                            $icon_img_id = isset($row['icon_img_id']) ? intval($row['icon_img_id']) : '';
                            $icon_img_url = $icon_img_id ? wp_get_attachment_image_url($icon_img_id, 'thumbnail') : '';
                            if ($url) {
                                echo '<a href="'.$url.'" class="wam-social-icon" style="display:inline-flex;align-items:center;justify-content:center;width:38px;height:38px;background:#21738a;color:#fff;border-radius:6px;font-size:20px;" target="_blank" rel="noopener" title="'.$name.'">';
                                if ($icon_img_url) {
                                    echo '<img src="'.$icon_img_url.'" alt="'.$name.'" style="width:20px;height:20px;object-fit:contain;display:block;">';
                                } elseif ($icon) {
                                    echo '<i class="'.$icon.'"></i>';
                                } elseif (stripos($name, 'linkedin') !== false) {
                                    echo '<i class="fab fa-linkedin-in"></i>';
                                } elseif (stripos($name, 'facebook') !== false) {
                                    echo '<i class="fab fa-facebook-f"></i>';
                                } elseif (stripos($name, 'instagram') !== false) {
                                    echo '<i class="fab fa-instagram"></i>';
                                } else {
                                    echo '<i class="fas fa-share"></i>';
                                }
                                echo '</a>';
                            }
                        }
                        ?>
                    </div>
                    <?php endif; ?>
  
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    private static function render_filters($shortcode_id) {
        $shortcode = get_post($shortcode_id);
        if (!$shortcode || $shortcode->post_type !== 'wam_shortcode') return;
        $meta = get_post_meta($shortcode_id);
        $show_filters = isset($meta['show_filters'][0]) ? $meta['show_filters'][0] : 'yes';
        $show_search = isset($meta['show_search'][0]) ? $meta['show_search'][0] : 'yes';
        if ($show_filters === 'no' && $show_search === 'no') return;
        // Inline CSS for filter bar and pills
        echo '<style>
        .wam-filters-bar { display: flex; align-items: flex-start; justify-content: space-between; gap: 24px; margin-bottom: 18px; }
        .wam-filters-btns { display: flex; gap: 8px; }
        .wam-filter-btn { background: #fafbfc; border: 1px solid #ccc; border-radius: 4px; padding: 8px 18px; font-size: 15px; cursor: pointer; transition: background 0.2s, border 0.2s; }
        .wam-filter-btn.active, .wam-filter-btn:focus { background: #e5e5e5; border-color: #888; }
        .wam-apply-filters { background: var(--wam-main-color) !important; color: white !important; border-color: var(--wam-main-color) !important; }
        .wam-apply-filters:hover { opacity: 0.9; }
        .wam-active-filters { display: flex; gap: 8px; margin-top: 10px; }
        .wam-filter-pill { background: #ededed; border: 1px solid #bbb; border-radius: 4px; padding: 6px 14px; font-size: 15px; display: flex; align-items: center; gap: 6px; }
        .wam-filter-pill .wam-remove-pill { background: none; border: none; color: #333; font-size: 16px; margin-left: 4px; cursor: pointer; }
        .wam-search-bar-wrap { display: flex; align-items: center; background: #222; border-radius: 2px; overflow: hidden; }
        .wam-search-bar { border: 1px solid #333; background: #white; color: #fff; padding: 10px 14px; font-size: 15px; outline: none; width: 160px; }
        .wam-search-btn { background: #333333; color: #fff; border: none; padding: 10px 16px; font-size: 18px; cursor: pointer; }
        @media (max-width: 700px) {
            button.wam-clear-filters.wam-filter-btn, button.wam-apply-filters.wam-filter-btn { margin-left: 0px !important; margin-top: 8px; }
            .wam-filters-bar { flex-direction: column; gap: 12px; }
            .wam-search-bar { width: 100px; }
            .wam-filters-bar button.wam-filter-btn { margin: 5px 5px; }
            .wam-filters-btns { display: ruby; gap: 18px; }
        }
        </style>';
        // Taxonomies (only show enabled ones)
        $taxonomies = [];
        if (!empty($meta['wam_sc_filter_location'][0])) $taxonomies['location'] = 'Location';
        if (!empty($meta['wam_sc_filter_industry'][0])) $taxonomies['industry'] = 'Industry';
        if (!empty($meta['wam_sc_filter_service'][0])) $taxonomies['service'] = 'Service';
        if (!empty($meta['wam_sc_filter_category'][0])) $taxonomies['category'] = 'Category';
        if (!empty($meta['wam_sc_filter_job_role'][0])) $taxonomies['job_role'] = 'Role';
        // Get all terms for each taxonomy
        $terms_by_tax = [];
        foreach ($taxonomies as $tax => $label) {
            $terms_by_tax[$tax] = get_terms(['taxonomy' => $tax, 'hide_empty' => true]);
        }
        // Render filter bar
        echo '<div class="wam-filters-bar">';
        // Filter buttons
        echo '<div class="wam-filters-btns">';
        echo '<button class="wam-filter-btn wam-filter-btn-all active" data-tax="all" data-term="">All</button>';
        foreach ($taxonomies as $tax => $label) {
            echo '<div class="wam-filter-dropdown-group" style="position:relative;">';
            echo '<button class="wam-filter-btn" data-tax="'.$tax.'" data-term="">'.$label.' <span style="font-size:12px;">▼</span></button>';
            // Dropdown
            echo '<div class="wam-filter-dropdown" style="display:none;position:absolute;z-index:10;left:0;top:110%;background:#fff;border:1px solid #ccc;border-radius:4px;box-shadow:0 2px 8px rgba(0,0,0,0.08);min-width:250px; font-size: 16px;">';
            foreach ($terms_by_tax[$tax] as $term) {
                echo '<label class="wam-filter-dropdown-item" style="display:flex;align-items:center;gap:8px;padding:8px 14px;cursor:pointer;">
                    <input type="checkbox" class="wam-filter-checkbox" data-tax="'.$tax.'" data-term="'.$term->slug.'">
                    '.esc_html($term->name).'
                </label>';
            }
            echo '</div>';
            echo '</div>';
        }
        // Add Apply Filter and Clear Filters buttons
        echo '<button class="wam-apply-filters wam-filter-btn" type="button" style="margin-left:16px;background:var(--wam-main-color);color:white;">Apply Filters</button>';
        echo '<button class="wam-clear-filters wam-filter-btn" type="button" style="margin-left:8px;">Clear Filters</button>';
        echo '</div>';
        // Search bar
        if ($show_search === 'yes') {
            echo '<div class="wam-search-bar-wrap">';
            echo '<input type="text" class="wam-search-bar" placeholder="Search">';
            echo '<button class="wam-search-btn">'
                .'<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="9" cy="9" r="7"/><line x1="16" y1="16" x2="13.5" y2="13.5"/></svg>'
                .'</button>';
            echo '</div>';
        }
        echo '</div>';
        // Active filter pills
        echo '<div class="wam-active-filters"></div>';
        $ajax_url = admin_url('admin-ajax.php');
        $wam_filter_nonce = wp_create_nonce('wam_filter_nonce');
        // JS for filter bar
        echo <<<JS
<script>
jQuery(function($){
    var shortcodeId = "{$shortcode_id}";
    var selectedFilters = {}; // { tax: [ {slug, label}, ... ] }
    var searchTerm = "";
    var teamContainer = $(".wam-team-grid[data-shortcode-id='" + shortcodeId + "']");
    // Initialize with default role if present, after DOM is ready
    (function ensureDefaultRoleInit() {
        var defaultRole = window["wamDefaultRole_" + shortcodeId];
        if (defaultRole && defaultRole.slug) {
            var roleBtn = $(".wam-filter-btn[data-tax='job_role']");
            var observer = new MutationObserver(function(mutations, obs) {
                var roleCheckbox = document.querySelector(".wam-filter-checkbox[data-tax='job_role'][data-term='" + defaultRole.slug + "']");
                if (roleCheckbox) {
                    roleCheckbox.checked = true;
                    // Trigger change event
                    var event = document.createEvent('HTMLEvents');
                    event.initEvent('change', true, false);
                    roleCheckbox.dispatchEvent(event);
                    selectedFilters["job_role"] = [{slug: defaultRole.slug, label: defaultRole.label}];
                    roleBtn.addClass("active");
                    $(".wam-filter-btn-all").removeClass("active");
                    updatePills();
                    $(".wam-active-filters").show();
                    // Auto-apply default role filter for initial load
                    ajaxFilter();
                    obs.disconnect();
                }
            });
            observer.observe(document.body, {childList: true, subtree: true});
            // Open the dropdown to trigger rendering
            roleBtn.trigger('click');
            // Optionally close after a short delay
            setTimeout(function(){ roleBtn.trigger('click'); }, 300);
        }
    })();
    // Dropdown logic
    $(document).on("click", ".wam-filter-btn[data-tax]", function(e){
        var tax = $(this).data("tax");
        if(tax==="all"){
            selectedFilters = {};
            $(".wam-filter-btn").removeClass("active");
            $(this).addClass("active");
            updatePills();
            // Don't auto-filter, wait for Apply button
            return;
        }
        $(".wam-filter-dropdown").hide();
        $(this).siblings(".wam-filter-dropdown").toggle();
        e.stopPropagation();
    });
    $(document).on("click", function(){ $(".wam-filter-dropdown").hide(); });
    // Multi-select dropdown item (checkbox version)
    $(document).on("change", ".wam-filter-checkbox", function(e){
        var tax = $(this).data("tax");
        var term = $(this).data("term");
        var label = $(this).parent().text().trim();
        if (!selectedFilters[tax]) selectedFilters[tax] = [];
        if ($(this).is(":checked")) {
            // Add if not already present
            if (selectedFilters[tax].findIndex(function(obj){ return obj.slug === term; }) === -1) {
                selectedFilters[tax].push({slug: term, label: label});
            }
        } else {
            // Remove
            selectedFilters[tax] = selectedFilters[tax].filter(function(obj){ return obj.slug !== term; });
            if (selectedFilters[tax].length === 0) {
                delete selectedFilters[tax];
            }
        }
        $(".wam-filter-btn-all").removeClass("active");
        if (selectedFilters[tax] && selectedFilters[tax].length > 0) {
            $(".wam-filter-btn[data-tax='"+tax+"']").addClass("active");
        } else {
            $(".wam-filter-btn[data-tax='"+tax+"']").removeClass("active");
        }
        updatePills();
        // Don't auto-filter, wait for Apply button
        e.stopPropagation();
    });
    // Remove pill
    $(document).on("click", ".wam-remove-pill", function(){
        var tax = $(this).data("tax");
        var term = $(this).data("term");
        if (selectedFilters[tax]) {
            selectedFilters[tax] = selectedFilters[tax].filter(function(obj){ return obj.slug !== term; });
            if (selectedFilters[tax].length === 0) {
                delete selectedFilters[tax];
                $(".wam-filter-btn[data-tax='"+tax+"']").removeClass("active");
            }
        }
        // Uncheck the checkbox
        $(".wam-filter-checkbox[data-tax='"+tax+"'][data-term='"+term+"']").prop("checked", false);
        updatePills();
        // Don't auto-filter, wait for Apply button
    });
    // Update pills
    function updatePills(){
        var pills = $(".wam-active-filters");
        pills.empty();
        $.each(selectedFilters, function(tax, arr){
            arr.forEach(function(obj){
                pills.append("<span class=\"wam-filter-pill\">"+obj.label+" <button class=\"wam-remove-pill\" data-tax=\""+tax+"\" data-term=\""+obj.slug+"\">×</button></span>");
            });
        });
    }
    // Search - now just updates the search term, doesn't auto-filter
    $(document).on("click", ".wam-search-btn", function(){
        searchTerm = $(this).siblings(".wam-search-bar").val();
        ajaxFilter(); // Search still triggers immediately for better UX
    });

    // Search on Enter key
    $(document).on("keypress", ".wam-search-bar", function(e){
        if (e.which === 13) { // Enter key
            searchTerm = $(this).val();
            ajaxFilter();
        }
    });

    // Apply Filters button
    $(document).on("click", ".wam-apply-filters", function(){
        ajaxFilter();
    });
    // AJAX filter
    function ajaxFilter(){
        var filters = {};
        $.each(selectedFilters, function(tax, arr){
            filters[tax] = arr.map(function(obj){ return obj.slug; });
        });
        console.log('Sending filters:', filters); // Debug
        $.ajax({
            url: "{$ajax_url}",
            type: "POST",
            data: {
                action: "wam_filter_team_members",
                shortcode_id: shortcodeId,
                search: searchTerm,
                filters: JSON.stringify(filters),
                nonce: "{$wam_filter_nonce}"
            },
            beforeSend: function() {
                teamContainer.html("<div style=\"text-align:center;padding:40px;\"><div style=\"color:#666;\">Loading...</div></div>");
            },
            success: function(response) {
                console.log('AJAX response:', response); // Debug
                if (response.success) {
                    teamContainer.html(response.data.html);
                } else {
                    teamContainer.html("<div style=\"text-align:center;padding:40px;color:#b00;\">Error loading results. Please try again.</div>");
                }
            },
            error: function(xhr, status, error) {
                teamContainer.html("<div style=\"text-align:center;padding:40px;color:#b00;\">AJAX error: " + error + "</div>");
            }
        });
    }
    // Highlight selected dropdown items on open (sync checkboxes)
    $(document).on("click", ".wam-filter-btn[data-tax]", function(e){
        var tax = $(this).data("tax");
        if (tax && tax !== "all") {
            var selected = (selectedFilters[tax]||[]).map(function(obj){ return obj.slug; });
            $(this).siblings(".wam-filter-dropdown").find(".wam-filter-checkbox").each(function(){
                var term = $(this).data("term");
                $(this).prop("checked", selected.indexOf(term) !== -1);
            });
        }
    });
    // Clear Filters button
    $(document).on("click", ".wam-clear-filters", function(){
        selectedFilters = {};
        searchTerm = "";
        $(".wam-filter-btn").removeClass("active");
        $(".wam-filter-btn-all").addClass("active");
        $(".wam-filter-checkbox").prop("checked", false);
        $(".wam-search-bar").val("");
        updatePills();
        ajaxFilter(); // Clear filters applies immediately
    });
});
</script>
JS;
    }
} 