/* Advisor Mo<PERSON>le <PERSON>end Styles */
.wam-advisor-module {
  --wam-main-color: #2271b1;
  --wam-card-bg: #fff;
  --wam-card-radius: 12px;
  --wam-font-family: inherit;
  --wam-font-color: #222;
}

.wam-filter-dropdown input.wam-filter-checkbox {
  accent-color: #227388;
}
h4.team-ttl {
  margin: 0;
}
.wam-advisor-module .wam-team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 32px;
  margin: 32px 0;
}
.wam-advisor-module .wam-team-card {
  background: var(--wam-card-bg);
  border-radius: var(--wam-card-radius);
  /*   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06); */
  border: 1px solid #ddd;
  padding: 32px 24px;
  text-align: center;
  font-family: var(--wam-font-family);
  color: var(--wam-font-color);
}
.wam-team-card h4.team-ttl {
  font-size: 26px;
}
/* Single Team Member Page Styles */
.wam-single-team-container {
  display: flex;
  flex-wrap: wrap;
  gap: 40px;
  align-items: flex-start;
}
.wam-single-team-main {
  flex: 2 1 400px;
}
.wam-single-team-header {
  display: flex;
  gap: 28px;
  align-items: flex-start;
}
.wam-single-team-img {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 50%;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
.wam-single-team-name {
  font-size: 2em;
  font-weight: 700;
  color: #e76b1c;
  line-height: 1.1;
}
.wam-single-team-role {
  font-size: 1.1em;
  font-weight: 600;
  margin-bottom: 2px;
}
.wam-single-team-title {
  font-size: 1.1em;
  color: #444;
  margin-bottom: 8px;
}
.wam-single-team-contact-row {
  display: flex;
  gap: 18px;
  align-items: center;
  margin: 8px 0 0 0;
  flex-wrap: wrap;
}
.wam-single-team-social-row {
  display: flex;
  gap: 10px;
  margin-top: 8px;
}
.wam-single-team-social-row a {
  color: #21738a;
  font-size: 1.1em;
}
.wam-single-team-desc {
  font-size: 1.2em;
  margin: 24px 0 18px 0;
  line-height: 1.5;
  color: #222;
  font-weight: bold;
}
.wam-single-team-content {
  margin-bottom: 24px;
}
.wam-single-team-section {
  margin-bottom: 18px;
}
.wam-single-team-section-title {
  font-size: 1.3em;
  font-weight: 700;
  margin-bottom: 6px;
}
.wam-single-team-section-content {
  font-size: 1.08em;
  line-height: 1.6;
}
.wam-single-team-sidebar {
  flex: 1 1 260px;
  min-width: 220px;
  max-width: 340px;
}
.wam-single-team-sidebar-box {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.07);
  padding: 28px 18px 18px 18px;
  margin-bottom: 24px;
}
.wam-single-team-sidebar-title {
  font-size: 1.5em;
  font-weight: 700;
  margin-bottom: 18px;
}
.wam-single-team-sidebar-btn {
  display: block;
  background: #e76b1c;
  color: #fff;
  font-weight: 600;
  text-align: center;
  padding: 12px 0;
  border-radius: 5px;
  text-decoration: none;
  margin-bottom: 18px;
}
.wam-single-team-sidebar-quote {
  font-size: 1.08em;
  color: #444;
  line-height: 1.5;
  /* background: #fafbfc; */
  padding: 16px 14px;
  border-radius: 8px;
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04); */
  margin-top: 10px;
}
.wam-team-img-wrap img {
  width: 140px !important;
  height: 140px !important;
  object-fit: cover;
  border-radius: 50% !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  transition: opacity 0.3s;
}
.wam-team-img-wrap .wam-team-img-secondary {
  width: 140px !important;
  height: 140px !important;
  object-fit: cover;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  opacity: 0;
  transition: opacity 0.3s;
}
@media (max-width: 900px) {
  .wam-single-team-container {
    flex-direction: column;
    gap: 24px;
  }
  .wam-single-team-sidebar {
    max-width: 100%;
  }
}
